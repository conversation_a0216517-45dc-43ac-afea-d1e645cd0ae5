import { Context } from "telegraf";
import { PREM_RELAYER_USERNAME } from "../../app.constants";
import { updateUserSession } from "../../services/session";
import { T } from "../../i18n";
import { botMessages } from "../../intl/messages";
import { createMarketplaceInlineKeyboard } from "../../utils/keyboards";
import { WithdrawGiftCallbackLogger } from "./withdraw-gift-callback.logger";

export const handleWithdrawGiftCallback = async (ctx: Context) => {
  try {
    // @ts-expect-error note
    const callbackData = ctx.callbackQuery?.data;
    const tgId = ctx.from?.id?.toString();

    if (!callbackData || !tgId) {
      await ctx.answerCbQuery("Invalid request");
      return;
    }

    // Extract gift ID from callback data (format: withdraw_gift_{giftId})
    const giftId = callbackData.replace("withdraw_gift_", "");

    if (!giftId) {
      await ctx.answerCbQuery("Invalid gift ID");
      return;
    }

    WithdrawGiftCallbackLogger.logWithdrawGiftCallbackStarted({
      tgId,
      giftId,
      chatId: ctx.chat?.id as number,
    });

    // Save the selected gift ID to the user's bot session
    await updateUserSession(tgId, {
      withdrawal_gift_id: giftId,
    });

    WithdrawGiftCallbackLogger.logGiftIdSavedToSession({
      tgId,
      giftId,
    });

    // Send instructions to the user
    const instructionMessage = T(
      ctx,
      botMessages.giftWithdrawalInstructions.id,
      {
        relayerUsername: PREM_RELAYER_USERNAME,
      }
    );

    await ctx.editMessageText(
      instructionMessage,
      createMarketplaceInlineKeyboard(ctx)
    );

    await ctx.answerCbQuery(T(ctx, botMessages.giftSelectedForWithdrawal.id));

    WithdrawGiftCallbackLogger.logWithdrawGiftCallbackCompleted({
      tgId,
      giftId,
    });
  } catch (error) {
    if (ctx.chat?.id) {
      WithdrawGiftCallbackLogger.logWithdrawGiftCallbackError({
        error,
        tgId: ctx.from?.id?.toString() ?? "unknown",
        chatId: ctx.chat?.id,
      });
    }

    await ctx.answerCbQuery(T(ctx, botMessages.botGenericError.id));
  }
};
