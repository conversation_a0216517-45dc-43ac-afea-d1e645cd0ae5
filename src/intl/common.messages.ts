import { defineMessages } from "@formatjs/intl";

export const commonMessages = defineMessages({
  telegramIdError: {
    id: "common.telegramIdError",
    defaultMessage: "❌ Unable to identify your Telegram ID. Please try again.",
  },
  genericError: {
    id: "common.genericError",
    defaultMessage:
      "❌ Failed to process your request. Please try again later.",
  },
  welcome: {
    id: "common.welcome",
    defaultMessage: "🛍️ Welcome to the {APP_NAME} Bot!",
  },
  help: {
    id: "common.help",
    defaultMessage:
      "Welcome to {APP_NAME}! \n \n{PREM_CHANNEL} - Community \n{PREM_SUPPORT_OFFICIAL} - Support \n{relayerUsername} - Gift Relayer",
  },
  botGenericError: {
    id: "common.botGenericError",
    defaultMessage: "Sorry, something went wrong. Please try again later.",
  },
  botDescription: {
    id: "common.botDescription",
    defaultMessage: `Welcome to {APP_NAME}🎁 – the first liquid pre-market for Telegram unupgraded gifts!\nWith {APP_NAME}, you can:\n\nAs buyer:\n\n🔓 Buy any unupgraded TG gift.\n💸 Resell for instant profit\n\nAs seller:\n\n🎁 Sell unupgraded TG gift with just 50% collateral.\n💰 Earn fees from resales\n\nEnjoy fast, safe, and easy gift trading!`,
  },
  botShortDescription: {
    id: "common.botShortDescription",
    defaultMessage: "🎁 {APP_NAME} - Telegram Gifts Marketplace",
  },
  withdrawalErrorFallback: {
    id: "common.withdrawalErrorFallback",
    defaultMessage: "Failed to withdraw gift",
  },
  giftWithdrawalInstructions: {
    id: "common.giftWithdrawalInstructions",
    defaultMessage:
      "✅ Gift selected for withdrawal! Now go to @{relayerUsername} and send the command 'get a gift' to complete the withdrawal.",
  },
  giftSelectedForWithdrawal: {
    id: "common.giftSelectedForWithdrawal",
    defaultMessage: "Gift selected for withdrawal",
  },
});
